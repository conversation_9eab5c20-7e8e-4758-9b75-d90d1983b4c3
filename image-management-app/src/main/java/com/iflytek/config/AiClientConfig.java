package com.iflytek.config;

import com.iflytek.domain.ai.model.AiClient;
import com.iflytek.domain.ai.service.AiClientNode;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;

@Configuration
@Slf4j
public class AiClientConfig {

    @Resource
    private AiClientNode aiClientNode;

    @Primary
    @Bean
    public ChatClient ChatClient()  {
        log.info("创建AI客户端");
        AiClient.AiClientModel model = AiClient.AiClientModel.builder()
                .baseUrl("https://api.chatanywhere.tech/v1")
                .apiKey("sk-ibicHR4fnQqAC3It37zSoA8BCSvdP4zlWEajc8oGJz1y9DX2")
                .completionsPath("/chat/completions")
                .embeddingsPath("/embeddings")
                .modelType("openai")
                .modelVersion("gpt-3.5-turbo")
                .timeout(60)
                .build();
        AiClient.AiClientToolMcp toolMcp = AiClient.AiClientToolMcp.builder()
                .mcpName("weather-tool")
                .transportType("sse")
                .transportConfigSse(AiClient.AiClientToolMcp.TransportConfigSse.builder()
//                        .baseUri("https://mcp.amap.com/sse?key=afc258f78a91f09c889ba3310332467f")
                        .baseUri("http://localhost:8099/sse")
                        .build())
                .requestTimeout(60)
                .build();
        AiClient aiClient = AiClient.builder()
                .clientId(1L)
//                .systemPrompt("你是一个问答专家，请回答用户的问题")
                .systemPrompt("你是一个图像专家，请根据用户描述搜索相应的图像")
                .model(model)
                .mcpServiceList(List.of(toolMcp))
                .build();
        return aiClientNode.createChatClient(aiClient);
    }
}
