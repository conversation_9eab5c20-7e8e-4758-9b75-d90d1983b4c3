<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.infrastructure.dao.AiClientAdvisorMapper">

    <resultMap id="BaseResultMap" type="com.iflytek.infrastructure.dao.po.AiClientAdvisorPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="advisor_name" property="advisorName" jdbcType="VARCHAR"/>
        <result column="advisor_type" property="advisorType" jdbcType="VARCHAR"/>
        <result column="order_num" property="orderNum" jdbcType="INTEGER"/>
        <result column="ext_param" property="extParam" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, advisor_name, advisor_type, order_num, ext_param, create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.iflytek.infrastructure.dao.po.AiClientAdvisorPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client_advisor (advisor_name, advisor_type, order_num, ext_param, create_time, update_time)
        VALUES (#{advisorName}, #{advisorType}, #{orderNum}, #{extParam}, NOW(), NOW())
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_advisor
        WHERE id = #{id}
    </select>

    <select id="selectByAdvisorName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_advisor
        WHERE advisor_name = #{advisorName}
    </select>

    <select id="selectByAdvisorType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_advisor
        WHERE advisor_type = #{advisorType}
        ORDER BY order_num , id DESC
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_advisor
        ORDER BY order_num , id DESC
    </select>

    <update id="updateById" parameterType="com.iflytek.infrastructure.dao.po.AiClientAdvisorPO">
        UPDATE ai_client_advisor
        SET advisor_name = #{advisorName},
            advisor_type = #{advisorType},
            order_num    = #{orderNum},
            ext_param    = #{extParam},
            update_time  = NOW()
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_client_advisor WHERE id = #{id}
    </delete>

</mapper>
