server:
  port: 8091

# 线程池配置
thread:
  pool:
    executor:
      config:
        core-pool-size: 20
        max-pool-size: 50
        keep-alive-time: 5000
        block-queue-size: 5000
        policy: CallerRunsPolicy

# MinIO 对象存储配置
minio:
  endpoint: ${MINIO_ENDPOINT:http://8.141.11.126:9000}
  access-key: ${MINIO_ACCESS_KEY:minioadmin}
  secret-key: ${MINIO_SECRET_KEY:minioadmin123}
  bucket-name: ${MINIO_BUCKET:image-storage}
  # 图片存储路径前缀
  image-path-prefix: images/
  # 缩略图路径前缀
  thumbnail-path-prefix: thumbnails/
  # 上传配置
  upload:
    max-file-size: 10MB
    allowed-formats: jpg,jpeg,png,gif,webp,svg
  # 访问配置
  public-read: true

# 数据库配置；启动时配置数据库资源信息
# 数据库配置
spring:
  datasource:
    url: *************************************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: org.mariadb.jdbc.Driver
    hikari:
      pool-name: MariaDB_HikariCP
      minimum-idle: 15
      idle-timeout: 180000
      maximum-pool-size: 25
      auto-commit: true
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  # Spring AI 配置
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:your-openai-api-key}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      embedding:
        options:
          model: text-embedding-3-small
          dimensions: 1536


# MyBatis 配置【如需使用记得打开】
mybatis:
  mapper-locations: classpath:/mybatis/mapper/*.xml
  config-location:  classpath:/mybatis/config/mybatis-config.xml

# 日志
logging:
  level:
    root: info
  config: classpath:logback-spring.xml
