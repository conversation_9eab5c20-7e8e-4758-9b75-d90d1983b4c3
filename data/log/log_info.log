25-08-08.20:31:50.926 [main            ] INFO  Application            - Starting Application using Java 17.0.12 with PID 11188 (D:\works\image-management\image-management-app\target\classes started by aofu in D:\works\image-management)
25-08-08.20:31:50.936 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-08.20:31:52.425 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'sqlSessionFactory' defined in class path resource [com/iflytek/config/DataSourceConfig.class]: Cannot register bean definition [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; fallback=false; factoryBeanName=dataSourceConfig; factoryMethodName=sqlSessionFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/iflytek/config/DataSourceConfig.class]] for bean 'sqlSessionFactory' since there is already [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; fallback=false; factoryBeanName=aiClientConfig; factoryMethodName=sqlSessionFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/iflytek/config/AiClientConfig.class]] bound.
25-08-08.20:31:52.438 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-08-08.20:31:52.468 [main            ] ERROR LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'sqlSessionFactory', defined in class path resource [com/iflytek/config/DataSourceConfig.class], could not be registered. A bean with that name has already been defined in class path resource [com/iflytek/config/AiClientConfig.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

25-08-08.20:33:30.911 [main            ] INFO  Application            - Starting Application using Java 17.0.12 with PID 25076 (D:\works\image-management\image-management-app\target\classes started by aofu in D:\works\image-management)
25-08-08.20:33:30.913 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-08.20:33:31.471 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'sqlSessionFactory' defined in class path resource [com/iflytek/config/DataSourceConfig.class]: Cannot register bean definition [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; fallback=false; factoryBeanName=dataSourceConfig; factoryMethodName=sqlSessionFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/iflytek/config/DataSourceConfig.class]] for bean 'sqlSessionFactory' since there is already [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; fallback=false; factoryBeanName=aiClientConfig; factoryMethodName=sqlSessionFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/iflytek/config/AiClientConfig.class]] bound.
25-08-08.20:33:31.477 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-08-08.20:33:31.490 [main            ] ERROR LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'sqlSessionFactory', defined in class path resource [com/iflytek/config/DataSourceConfig.class], could not be registered. A bean with that name has already been defined in class path resource [com/iflytek/config/AiClientConfig.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

25-08-08.20:34:12.090 [main            ] INFO  Application            - Starting Application using Java 17.0.12 with PID 6504 (D:\works\image-management\image-management-app\target\classes started by aofu in D:\works\image-management)
25-08-08.20:34:12.092 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-08.20:34:12.742 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'sqlSessionFactory' defined in class path resource [com/iflytek/config/DataSourceConfig.class]: Cannot register bean definition [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; fallback=false; factoryBeanName=dataSourceConfig; factoryMethodName=sqlSessionFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/iflytek/config/DataSourceConfig.class]] for bean 'sqlSessionFactory' since there is already [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; fallback=false; factoryBeanName=aiClientConfig; factoryMethodName=sqlSessionFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/iflytek/config/AiClientConfig.class]] bound.
25-08-08.20:34:12.749 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-08-08.20:34:12.766 [main            ] ERROR LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'sqlSessionFactory', defined in class path resource [com/iflytek/config/DataSourceConfig.class], could not be registered. A bean with that name has already been defined in class path resource [com/iflytek/config/AiClientConfig.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

25-08-08.20:35:01.374 [main            ] INFO  Application            - Starting Application using Java 17.0.12 with PID 21300 (D:\works\image-management\image-management-app\target\classes started by aofu in D:\works\image-management)
25-08-08.20:35:01.377 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-08.20:35:02.257 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-08.20:35:03.409 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-08.20:35:03.444 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-08.20:35:03.447 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-08.20:35:03.448 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-08.20:35:03.636 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-08.20:35:03.637 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2207 ms
25-08-08.20:35:03.841 [main            ] INFO  AiClientConfig         - 创建AI客户端
25-08-08.20:35:06.104 [HttpClient-1-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=amap-sse-server, version=1.0.0] and Instructions null
25-08-08.20:35:06.301 [main            ] INFO  AiClientNodeImpl       - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], serverInfo=Implementation[name=amap-sse-server, version=1.0.0], instructions=null]
25-08-08.20:35:06.847 [main            ] INFO  DataSourceConfig       - 创建数据源 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.20:35:06.854 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Starting...
25-08-08.20:35:07.119 [main            ] INFO  HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@782e6b40
25-08-08.20:35:07.124 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Start completed.
25-08-08.20:35:07.124 [main            ] INFO  DataSourceConfig       - 数据源创建成功 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.20:35:07.278 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/iflytek/config/DataSourceConfig.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [D:\works\image-management\image-management-app\target\classes\mybatis\mapper\frame_case_mapper.xml]'
25-08-08.20:35:07.278 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown initiated...
25-08-08.20:35:07.402 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown completed.
25-08-08.20:35:07.407 [main            ] INFO  StandardService        - Stopping service [Tomcat]
25-08-08.20:35:07.420 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-08-08.20:35:07.438 [main            ] ERROR SpringApplication      - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/iflytek/config/DataSourceConfig.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [D:\works\image-management\image-management-app\target\classes\mybatis\mapper\frame_case_mapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:346)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1155)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1121)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1056)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.iflytek.Application.main(Application.java:12)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [D:\works\image-management\image-management-app\target\classes\mybatis\mapper\frame_case_mapper.xml]'
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 21 common frames omitted
Caused by: java.io.IOException: Failed to parse mapping resource: 'file [D:\works\image-management\image-management-app\target\classes\mybatis\mapper\frame_case_mapper.xml]'
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:695)
	at org.mybatis.spring.SqlSessionFactoryBean.afterPropertiesSet(SqlSessionFactoryBean.java:572)
	at org.mybatis.spring.SqlSessionFactoryBean.getObject(SqlSessionFactoryBean.java:712)
	at com.iflytek.config.DataSourceConfig.sqlSessionFactory(DataSourceConfig.java:85)
	at com.iflytek.config.DataSourceConfig$$SpringCGLIB$$0.CGLIB$sqlSessionFactory$1(<generated>)
	at com.iflytek.config.DataSourceConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:372)
	at com.iflytek.config.DataSourceConfig$$SpringCGLIB$$0.sqlSessionFactory(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	... 24 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\works\image-management\image-management-app\target\classes\mybatis\mapper\frame_case_mapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.iflytek.infrastructure.persistent.po.A'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.iflytek.infrastructure.persistent.po.A
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:125)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:98)
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:693)
	... 37 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.iflytek.infrastructure.persistent.po.A'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.iflytek.infrastructure.persistent.po.A
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:103)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:222)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:214)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElements(XMLMapperBuilder.java:206)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:121)
	... 39 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.iflytek.infrastructure.persistent.po.A'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.iflytek.infrastructure.persistent.po.A
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:128)
	at org.apache.ibatis.builder.BaseBuilder.resolveAlias(BaseBuilder.java:132)
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:101)
	... 43 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.iflytek.infrastructure.persistent.po.A
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:226)
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:103)
	at org.apache.ibatis.io.Resources.classForName(Resources.java:322)
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:124)
	... 45 common frames omitted
25-08-08.20:35:35.538 [main            ] INFO  Application            - Starting Application using Java 17.0.12 with PID 7160 (D:\works\image-management\image-management-app\target\classes started by aofu in D:\works\image-management)
25-08-08.20:35:35.540 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-08.20:35:36.326 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[com.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-08.20:35:36.917 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-08.20:35:36.933 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-08.20:35:36.935 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-08.20:35:36.936 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-08.20:35:37.009 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-08.20:35:37.010 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1421 ms
25-08-08.20:35:37.097 [main            ] INFO  AiClientConfig         - 创建AI客户端
25-08-08.20:35:38.687 [HttpClient-1-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=amap-sse-server, version=1.0.0] and Instructions null
25-08-08.20:35:38.915 [main            ] INFO  AiClientNodeImpl       - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], serverInfo=Implementation[name=amap-sse-server, version=1.0.0], instructions=null]
25-08-08.20:35:39.438 [main            ] INFO  DataSourceConfig       - 创建数据源 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.20:35:39.444 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Starting...
25-08-08.20:35:39.806 [main            ] INFO  HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@66e1b2a
25-08-08.20:35:39.807 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Start completed.
25-08-08.20:35:39.808 [main            ] INFO  DataSourceConfig       - 数据源创建成功 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.20:35:39.818 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/iflytek/config/DataSourceConfig.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: class path resource [mybatis/mapper/] cannot be resolved to URL because it does not exist
25-08-08.20:35:39.818 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown initiated...
25-08-08.20:35:39.838 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown completed.
25-08-08.20:35:39.843 [main            ] INFO  StandardService        - Stopping service [Tomcat]
25-08-08.20:35:39.861 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-08-08.20:35:39.890 [main            ] ERROR SpringApplication      - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/iflytek/config/DataSourceConfig.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: class path resource [mybatis/mapper/] cannot be resolved to URL because it does not exist
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:346)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1155)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1121)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1056)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.iflytek.Application.main(Application.java:12)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: class path resource [mybatis/mapper/] cannot be resolved to URL because it does not exist
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 21 common frames omitted
Caused by: java.io.FileNotFoundException: class path resource [mybatis/mapper/] cannot be resolved to URL because it does not exist
	at org.springframework.core.io.ClassPathResource.getURL(ClassPathResource.java:230)
	at org.springframework.core.io.support.PathMatchingResourcePatternResolver.findPathMatchingResources(PathMatchingResourcePatternResolver.java:701)
	at org.springframework.core.io.support.PathMatchingResourcePatternResolver.getResources(PathMatchingResourcePatternResolver.java:366)
	at com.iflytek.config.DataSourceConfig.sqlSessionFactory(DataSourceConfig.java:83)
	at com.iflytek.config.DataSourceConfig$$SpringCGLIB$$0.CGLIB$sqlSessionFactory$0(<generated>)
	at com.iflytek.config.DataSourceConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:372)
	at com.iflytek.config.DataSourceConfig$$SpringCGLIB$$0.sqlSessionFactory(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	... 24 common frames omitted
25-08-08.20:38:20.735 [main            ] INFO  Application            - Starting Application using Java 17.0.12 with PID 18276 (D:\works\image-management\image-management-app\target\classes started by aofu in D:\works\image-management)
25-08-08.20:38:20.735 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-08.20:38:22.882 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-08.20:38:22.924 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-08.20:38:22.930 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-08.20:38:22.931 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-08.20:38:23.089 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-08.20:38:23.090 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2306 ms
25-08-08.20:38:23.297 [main            ] INFO  AiClientConfig         - 创建AI客户端
25-08-08.20:38:25.105 [HttpClient-1-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=amap-sse-server, version=1.0.0] and Instructions null
25-08-08.20:38:25.258 [main            ] INFO  AiClientNodeImpl       - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], serverInfo=Implementation[name=amap-sse-server, version=1.0.0], instructions=null]
25-08-08.20:38:25.797 [main            ] INFO  DataSourceConfig       - 创建数据源 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.20:38:25.804 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Starting...
25-08-08.20:38:26.092 [main            ] INFO  HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@5a6f6cac
25-08-08.20:38:26.094 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Start completed.
25-08-08.20:38:26.094 [main            ] INFO  DataSourceConfig       - 数据源创建成功 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.20:38:26.694 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-08.20:38:27.813 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'openAiAudioSpeechModel' defined in class path resource [org/springframework/ai/model/openai/autoconfigure/OpenAiAudioSpeechAutoConfiguration.class]: Failed to instantiate [org.springframework.ai.openai.OpenAiAudioSpeechModel]: Factory method 'openAiAudioSpeechModel' threw exception with message: OpenAI API key must be set. Use the connection property: spring.ai.openai.api-key or spring.ai.openai.speech.api-key property.
25-08-08.20:38:27.815 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown initiated...
25-08-08.20:38:27.991 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown completed.
25-08-08.20:38:27.997 [main            ] INFO  StandardService        - Stopping service [Tomcat]
25-08-08.20:38:28.021 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-08-08.20:38:28.069 [main            ] ERROR SpringApplication      - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'openAiAudioSpeechModel' defined in class path resource [org/springframework/ai/model/openai/autoconfigure/OpenAiAudioSpeechAutoConfiguration.class]: Failed to instantiate [org.springframework.ai.openai.OpenAiAudioSpeechModel]: Factory method 'openAiAudioSpeechModel' threw exception with message: OpenAI API key must be set. Use the connection property: spring.ai.openai.api-key or spring.ai.openai.speech.api-key property.
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:346)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1155)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1121)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1056)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.iflytek.Application.main(Application.java:12)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.ai.openai.OpenAiAudioSpeechModel]: Factory method 'openAiAudioSpeechModel' threw exception with message: OpenAI API key must be set. Use the connection property: spring.ai.openai.api-key or spring.ai.openai.speech.api-key property.
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 21 common frames omitted
Caused by: java.lang.IllegalArgumentException: OpenAI API key must be set. Use the connection property: spring.ai.openai.api-key or spring.ai.openai.speech.api-key property.
	at org.springframework.util.Assert.hasText(Assert.java:253)
	at org.springframework.ai.model.openai.autoconfigure.OpenAIAutoConfigurationUtil.resolveConnectionProperties(OpenAIAutoConfigurationUtil.java:59)
	at org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechAutoConfiguration.openAiAudioSpeechModel(OpenAiAudioSpeechAutoConfiguration.java:68)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	... 24 common frames omitted
25-08-08.20:40:06.789 [main            ] INFO  Application            - Starting Application using Java 17.0.12 with PID 16248 (D:\works\image-management\image-management-app\target\classes started by aofu in D:\works\image-management)
25-08-08.20:40:06.792 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-08.20:40:08.257 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-08.20:40:08.274 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-08.20:40:08.277 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-08.20:40:08.278 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-08.20:40:08.356 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-08.20:40:08.357 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1492 ms
25-08-08.20:40:08.446 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'aiController': Injection of resource dependencies failed
25-08-08.20:40:08.455 [main            ] INFO  StandardService        - Stopping service [Tomcat]
25-08-08.20:40:08.496 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-08-08.20:40:08.564 [main            ] ERROR LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'org.springframework.ai.chat.client.ChatClient' that could not be found.


Action:

Consider defining a bean of type 'org.springframework.ai.chat.client.ChatClient' in your configuration.

25-08-08.20:40:54.936 [main            ] INFO  Application            - Starting Application using Java 17.0.12 with PID 24892 (D:\works\image-management\image-management-app\target\classes started by aofu in D:\works\image-management)
25-08-08.20:40:54.937 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-08.20:40:56.418 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-08.20:40:56.441 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-08.20:40:56.444 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-08.20:40:56.445 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-08.20:40:56.563 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-08.20:40:56.563 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1585 ms
25-08-08.20:40:56.745 [main            ] INFO  DataSourceConfig       - 创建数据源 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.20:40:56.768 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Starting...
25-08-08.20:40:57.362 [main            ] INFO  HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@677349fb
25-08-08.20:40:57.369 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Start completed.
25-08-08.20:40:57.369 [main            ] INFO  DataSourceConfig       - 数据源创建成功 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.20:40:58.533 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-08.20:41:00.790 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'openAiAudioSpeechModel' defined in class path resource [org/springframework/ai/model/openai/autoconfigure/OpenAiAudioSpeechAutoConfiguration.class]: Failed to instantiate [org.springframework.ai.openai.OpenAiAudioSpeechModel]: Factory method 'openAiAudioSpeechModel' threw exception with message: OpenAI API key must be set. Use the connection property: spring.ai.openai.api-key or spring.ai.openai.speech.api-key property.
25-08-08.20:41:00.793 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown initiated...
25-08-08.20:41:01.032 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown completed.
25-08-08.20:41:01.036 [main            ] INFO  StandardService        - Stopping service [Tomcat]
25-08-08.20:41:01.052 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-08-08.20:41:01.075 [main            ] ERROR SpringApplication      - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'openAiAudioSpeechModel' defined in class path resource [org/springframework/ai/model/openai/autoconfigure/OpenAiAudioSpeechAutoConfiguration.class]: Failed to instantiate [org.springframework.ai.openai.OpenAiAudioSpeechModel]: Factory method 'openAiAudioSpeechModel' threw exception with message: OpenAI API key must be set. Use the connection property: spring.ai.openai.api-key or spring.ai.openai.speech.api-key property.
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1361)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1191)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:346)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1155)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1121)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1056)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.iflytek.Application.main(Application.java:12)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.ai.openai.OpenAiAudioSpeechModel]: Factory method 'openAiAudioSpeechModel' threw exception with message: OpenAI API key must be set. Use the connection property: spring.ai.openai.api-key or spring.ai.openai.speech.api-key property.
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 21 common frames omitted
Caused by: java.lang.IllegalArgumentException: OpenAI API key must be set. Use the connection property: spring.ai.openai.api-key or spring.ai.openai.speech.api-key property.
	at org.springframework.util.Assert.hasText(Assert.java:253)
	at org.springframework.ai.model.openai.autoconfigure.OpenAIAutoConfigurationUtil.resolveConnectionProperties(OpenAIAutoConfigurationUtil.java:59)
	at org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechAutoConfiguration.openAiAudioSpeechModel(OpenAiAudioSpeechAutoConfiguration.java:68)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	... 24 common frames omitted
25-08-08.20:43:30.012 [main            ] INFO  Application            - Starting Application using Java 17.0.12 with PID 21916 (D:\works\image-management\image-management-app\target\classes started by aofu in D:\works\image-management)
25-08-08.20:43:30.013 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-08.20:43:32.082 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-08.20:43:32.120 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-08.20:43:32.123 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-08.20:43:32.123 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-08.20:43:32.338 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-08.20:43:32.338 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2274 ms
25-08-08.20:43:32.522 [main            ] INFO  AiClientConfig         - 创建AI客户端
25-08-08.20:43:34.040 [HttpClient-1-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=amap-sse-server, version=1.0.0] and Instructions null
25-08-08.20:43:34.291 [main            ] INFO  AiClientNodeImpl       - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], serverInfo=Implementation[name=amap-sse-server, version=1.0.0], instructions=null]
25-08-08.20:43:34.787 [main            ] INFO  DataSourceConfig       - 创建数据源 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.20:43:34.796 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Starting...
25-08-08.20:43:35.035 [main            ] INFO  HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@11bdab37
25-08-08.20:43:35.037 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Start completed.
25-08-08.20:43:35.037 [main            ] INFO  DataSourceConfig       - 数据源创建成功 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.20:43:35.638 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-08.20:43:36.997 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8091"]
25-08-08.20:43:37.028 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8091 (http) with context path '/'
25-08-08.20:43:37.053 [main            ] INFO  Application            - Started Application in 7.65 seconds (process running for 8.296)
25-08-08.20:44:07.917 [SpringApplicationShutdownHook] INFO  GracefulShutdown       - Commencing graceful shutdown. Waiting for active requests to complete
25-08-08.20:44:08.096 [tomcat-shutdown ] INFO  GracefulShutdown       - Graceful shutdown complete
25-08-08.20:44:08.099 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown initiated...
25-08-08.20:44:08.324 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown completed.
25-08-08.20:44:34.146 [main            ] INFO  Application            - Starting Application using Java 17.0.12 with PID 19520 (D:\works\image-management\image-management-app\target\classes started by aofu in D:\works\image-management)
25-08-08.20:44:34.147 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-08.20:44:35.462 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-08.20:44:35.472 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-08.20:44:35.473 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-08.20:44:35.473 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-08.20:44:35.525 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-08.20:44:35.525 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1320 ms
25-08-08.20:44:35.618 [main            ] INFO  AiClientConfig         - 创建AI客户端
25-08-08.20:44:36.759 [HttpClient-1-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=amap-sse-server, version=1.0.0] and Instructions null
25-08-08.20:44:36.927 [main            ] INFO  AiClientNodeImpl       - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], serverInfo=Implementation[name=amap-sse-server, version=1.0.0], instructions=null]
25-08-08.20:44:37.452 [main            ] INFO  DataSourceConfig       - 创建数据源 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.20:44:37.458 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Starting...
25-08-08.20:44:37.713 [main            ] INFO  HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@34549979
25-08-08.20:44:37.715 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Start completed.
25-08-08.20:44:37.715 [main            ] INFO  DataSourceConfig       - 数据源创建成功 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.20:44:38.166 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-08.20:44:39.561 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8091"]
25-08-08.20:44:39.607 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8091 (http) with context path '/'
25-08-08.20:44:39.632 [main            ] INFO  Application            - Started Application in 5.977 seconds (process running for 6.243)
25-08-08.20:45:09.767 [http-nio-8091-exec-1] INFO  [/]                    - Initializing Spring DispatcherServlet 'dispatcherServlet'
25-08-08.20:45:09.767 [http-nio-8091-exec-1] INFO  DispatcherServlet      - Initializing Servlet 'dispatcherServlet'
25-08-08.20:45:09.767 [http-nio-8091-exec-1] INFO  DispatcherServlet      - Completed initialization in 0 ms
25-08-08.20:45:16.384 [http-nio-8091-exec-1] INFO  AiController           - AI客户端对话成功: message=北京今天的天气
25-08-08.20:45:27.520 [SpringApplicationShutdownHook] INFO  GracefulShutdown       - Commencing graceful shutdown. Waiting for active requests to complete
25-08-08.20:45:27.698 [tomcat-shutdown ] INFO  GracefulShutdown       - Graceful shutdown complete
25-08-08.20:45:27.701 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown initiated...
25-08-08.20:45:27.933 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown completed.
25-08-08.21:28:32.422 [main            ] INFO  Application            - Starting Application using Java 17.0.12 with PID 14556 (D:\works\image-management\image-management-app\target\classes started by aofu in D:\works\image-management)
25-08-08.21:28:32.424 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-08.21:28:33.836 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-08.21:28:33.852 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-08.21:28:33.854 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-08.21:28:33.854 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-08.21:28:33.930 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-08.21:28:33.931 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1470 ms
25-08-08.21:28:34.016 [main            ] INFO  AiClientConfig         - 创建AI客户端
25-08-08.21:28:34.505 [HttpClient-1-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=CompletionCapabilities[], experimental=null, logging=LoggingCapabilities[], prompts=PromptCapabilities[listChanged=true], resources=ResourceCapabilities[subscribe=false, listChanged=true], tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=mcp-server-image-search, version=1.0.0] and Instructions 图片搜索MCP服务器，提供语义搜索、条件筛选和详情获取功能
25-08-08.21:28:34.521 [main            ] INFO  AiClientNodeImpl       - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=CompletionCapabilities[], experimental=null, logging=LoggingCapabilities[], prompts=PromptCapabilities[listChanged=true], resources=ResourceCapabilities[subscribe=false, listChanged=true], tools=ToolCapabilities[listChanged=true]], serverInfo=Implementation[name=mcp-server-image-search, version=1.0.0], instructions=图片搜索MCP服务器，提供语义搜索、条件筛选和详情获取功能]
25-08-08.21:28:34.638 [main            ] INFO  DataSourceConfig       - 创建数据源 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.21:28:34.642 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Starting...
25-08-08.21:28:34.914 [main            ] INFO  HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@11bdab37
25-08-08.21:28:34.915 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Start completed.
25-08-08.21:28:34.915 [main            ] INFO  DataSourceConfig       - 数据源创建成功 - URL: *************************************************************************************************************************************************************************, 驱动: org.mariadb.jdbc.Driver
25-08-08.21:28:35.239 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-08.21:28:35.793 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8091"]
25-08-08.21:28:35.807 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8091 (http) with context path '/'
25-08-08.21:28:35.818 [main            ] INFO  Application            - Started Application in 3.998 seconds (process running for 4.475)
25-08-08.21:30:24.179 [http-nio-8091-exec-1] INFO  [/]                    - Initializing Spring DispatcherServlet 'dispatcherServlet'
25-08-08.21:30:24.179 [http-nio-8091-exec-1] INFO  DispatcherServlet      - Initializing Servlet 'dispatcherServlet'
25-08-08.21:30:24.180 [http-nio-8091-exec-1] INFO  DispatcherServlet      - Completed initialization in 1 ms
25-08-08.21:30:27.508 [http-nio-8091-exec-1] INFO  AiController           - AI客户端对话成功: message=请帮我找标签为小猫的两张图片
25-08-08.21:30:59.391 [SpringApplicationShutdownHook] INFO  GracefulShutdown       - Commencing graceful shutdown. Waiting for active requests to complete
25-08-08.21:30:59.576 [tomcat-shutdown ] INFO  GracefulShutdown       - Graceful shutdown complete
25-08-08.21:30:59.595 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown initiated...
25-08-08.21:30:59.823 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown completed.
