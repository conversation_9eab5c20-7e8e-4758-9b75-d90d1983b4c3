package com.iflytek.trigger.http;

import com.iflytek.domain.ai.model.AiClient;
import com.iflytek.domain.ai.service.AiClientNode;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/ai-client")
public class AiController {

    @Resource
    private ChatClient chatClient;

    /**
     * 测试AI客户端对话
     */
    @PostMapping("/chat")
    public Map<String, Object> chat(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();

        String userMessage = request.get("message");
        if (userMessage == null || userMessage.trim().isEmpty()) {
            result.put("success", false);
            result.put("", "消息内容不能为空");
            return result;
        }

        String response = chatClient.prompt()
                .user(userMessage)
                .call()
                .content();

        result.put("success", true);
        result.put("response", response);
        log.info("AI客户端对话成功: message={}", userMessage);


        return result;
    }
}
